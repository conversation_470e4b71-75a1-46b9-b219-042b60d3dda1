// Configuration for the Jishu app

// Replace this with the IP address shown when running the Flask backend
// You'll need to update this whenever your IP address changes
// IMPORTANT: You need to use the correct IP address to connect to your backend
// For Android emulator: use ********:5000
// For physical device: use your computer's actual IP address (e.g., *************:5000)
// For iOS simulator: use localhost:5000

// Try each of these options if you're having connection issues:
// export const API_URL = 'http://localhost:5000';  // For USB-connected device with adb reverse
// export const API_URL = 'http://********:5000';  // For Android emulator
export const API_URL = 'http://*************:5000';  // For device on same WiFi network

// IMPORTANT: For USB-connected Android devices:
// 1. Make sure USB debugging is enabled on your device
// 2. Run 'adb reverse tcp:5000 tcp:5000' in your terminal
// 3. Use 'http://localhost:5000' as the API URL
//
// For WiFi-connected devices:
// 1. Make sure your device is on the same network as your computer
// 2. Use your computer's IP address (e.g., 'http://***********:5000')

// Authentication settings
export const AUTH_CONFIG = {
  tokenStorageKey: 'accessToken',
  refreshTokenStorageKey: 'refreshToken',
  maxRetakes: 3, // Maximum number of retakes per purchase
};

// App settings
export const APP_CONFIG = {
  appName: 'Jishu',
  version: '1.0.0',
};

export default {
  API_URL,
  AUTH_CONFIG,
  APP_CONFIG,
};
