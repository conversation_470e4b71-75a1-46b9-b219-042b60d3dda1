import requests
import json

# Test the questions API endpoint
def test_questions_api():
    base_url = "http://localhost:5000"
    
    # Test getting questions for subject ID 4 (Mathematics)
    subject_id = 4
    url = f"{base_url}/api/questions/subject/{subject_id}?count=10"
    
    print(f"Testing questions API: {url}")
    
    try:
        response = requests.get(url)
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"Number of questions returned: {len(data)}")
                
                if data:
                    print("\nFirst question:")
                    first_question = data[0]
                    for key, value in first_question.items():
                        print(f"  {key}: {value}")
                else:
                    print("No questions returned")
                    
            except json.JSONDecodeError as e:
                print(f"JSON decode error: {e}")
                print(f"Raw response: {response.text[:500]}...")
        else:
            print(f"Error response: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"Request error: {e}")

if __name__ == "__main__":
    test_questions_api()
