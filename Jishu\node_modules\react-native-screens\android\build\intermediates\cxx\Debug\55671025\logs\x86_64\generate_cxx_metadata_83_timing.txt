# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 14ms
generate_cxx_metadata completed in 25ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 10ms]
  create-invalidation-state 24ms
generate_cxx_metadata completed in 43ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 15ms]
  create-invalidation-state 23ms
generate_cxx_metadata completed in 38ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 13ms]
  create-invalidation-state 18ms
generate_cxx_metadata completed in 35ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 16ms
  [gap of 22ms]
generate_cxx_metadata completed in 42ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 14ms]
  create-invalidation-state 23ms
generate_cxx_metadata completed in 45ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 20ms]
  create-invalidation-state 36ms
  [gap of 14ms]
generate_cxx_metadata completed in 70ms

