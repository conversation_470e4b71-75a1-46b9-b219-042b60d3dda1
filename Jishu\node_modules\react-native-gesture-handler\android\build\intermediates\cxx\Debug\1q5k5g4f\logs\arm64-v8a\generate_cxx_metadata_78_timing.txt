# C/C++ build system timings
generate_cxx_metadata
  [gap of 28ms]
  create-invalidation-state 31ms
  write-metadata-json-to-file 21ms
generate_cxx_metadata completed in 89ms

# C/C++ build system timings
generate_cxx_metadata 14ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 72ms]
  create-invalidation-state 78ms
  [gap of 30ms]
  write-metadata-json-to-file 31ms
generate_cxx_metadata completed in 211ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 33ms]
  create-invalidation-state 39ms
  [gap of 10ms]
  write-metadata-json-to-file 21ms
generate_cxx_metadata completed in 103ms

