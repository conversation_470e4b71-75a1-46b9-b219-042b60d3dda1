from app import create_app, db
from app.models.exam import QuestionWithOptions, Subject, ExamCategory

app = create_app()

def add_sample_questions():
    with app.app_context():
        # Get all subjects
        subjects = Subject.query.all()
        print(f"Found {len(subjects)} subjects")
        
        for subject in subjects:
            print(f"Adding questions for subject: {subject.subject_name} (ID: {subject.id})")
            
            # Check if questions already exist for this subject
            existing_questions = QuestionWithOptions.query.filter_by(subject_id=subject.id).count()
            if existing_questions > 0:
                print(f"  Subject already has {existing_questions} questions, skipping...")
                continue
            
            # Add sample questions based on subject name
            if 'physics' in subject.subject_name.lower():
                questions = get_physics_questions(subject.id)
            elif 'chemistry' in subject.subject_name.lower():
                questions = get_chemistry_questions(subject.id)
            elif 'biology' in subject.subject_name.lower() or 'botany' in subject.subject_name.lower() or 'zoology' in subject.subject_name.lower():
                questions = get_biology_questions(subject.id)
            elif 'math' in subject.subject_name.lower():
                questions = get_math_questions(subject.id)
            else:
                questions = get_general_questions(subject.id, subject.subject_name)
            
            # Add questions to database
            for question_data in questions:
                question = QuestionWithOptions(**question_data)
                db.session.add(question)
            
            print(f"  Added {len(questions)} questions")
        
        db.session.commit()
        print("All sample questions added successfully!")

def get_physics_questions(subject_id):
    return [
        {
            'subject_id': subject_id,
            'text': 'What is the SI unit of force?',
            'option_a': 'Newton',
            'option_b': 'Joule',
            'option_c': 'Watt',
            'option_d': 'Pascal',
            'correct_option': 'A',
            'explanation': 'The SI unit of force is Newton (N), named after Sir Isaac Newton.',
            'difficulty': 'easy'
        },
        {
            'subject_id': subject_id,
            'text': 'The acceleration due to gravity on Earth is approximately:',
            'option_a': '8.9 m/s²',
            'option_b': '9.8 m/s²',
            'option_c': '10.2 m/s²',
            'option_d': '11.1 m/s²',
            'correct_option': 'B',
            'explanation': 'The acceleration due to gravity on Earth is approximately 9.8 m/s².',
            'difficulty': 'easy'
        },
        {
            'subject_id': subject_id,
            'text': 'Which law states that for every action, there is an equal and opposite reaction?',
            'option_a': 'First law of motion',
            'option_b': 'Second law of motion',
            'option_c': 'Third law of motion',
            'option_d': 'Law of gravitation',
            'correct_option': 'C',
            'explanation': 'Newton\'s third law of motion states that for every action, there is an equal and opposite reaction.',
            'difficulty': 'medium'
        },
        {
            'subject_id': subject_id,
            'text': 'The speed of light in vacuum is:',
            'option_a': '3 × 10⁸ m/s',
            'option_b': '3 × 10⁶ m/s',
            'option_c': '3 × 10¹⁰ m/s',
            'option_d': '3 × 10⁴ m/s',
            'correct_option': 'A',
            'explanation': 'The speed of light in vacuum is approximately 3 × 10⁸ m/s.',
            'difficulty': 'medium'
        },
        {
            'subject_id': subject_id,
            'text': 'What is the formula for kinetic energy?',
            'option_a': 'KE = mv',
            'option_b': 'KE = ½mv²',
            'option_c': 'KE = mv²',
            'option_d': 'KE = ½mv',
            'correct_option': 'B',
            'explanation': 'The formula for kinetic energy is KE = ½mv², where m is mass and v is velocity.',
            'difficulty': 'medium'
        }
    ]

def get_chemistry_questions(subject_id):
    return [
        {
            'subject_id': subject_id,
            'text': 'What is the chemical symbol for gold?',
            'option_a': 'Go',
            'option_b': 'Gd',
            'option_c': 'Au',
            'option_d': 'Ag',
            'correct_option': 'C',
            'explanation': 'The chemical symbol for gold is Au, derived from the Latin word "aurum".',
            'difficulty': 'easy'
        },
        {
            'subject_id': subject_id,
            'text': 'What is the atomic number of carbon?',
            'option_a': '4',
            'option_b': '6',
            'option_c': '8',
            'option_d': '12',
            'correct_option': 'B',
            'explanation': 'Carbon has an atomic number of 6, meaning it has 6 protons in its nucleus.',
            'difficulty': 'easy'
        },
        {
            'subject_id': subject_id,
            'text': 'Which gas is most abundant in Earth\'s atmosphere?',
            'option_a': 'Oxygen',
            'option_b': 'Carbon dioxide',
            'option_c': 'Nitrogen',
            'option_d': 'Argon',
            'correct_option': 'C',
            'explanation': 'Nitrogen makes up about 78% of Earth\'s atmosphere.',
            'difficulty': 'medium'
        },
        {
            'subject_id': subject_id,
            'text': 'What is the pH of pure water at 25°C?',
            'option_a': '6',
            'option_b': '7',
            'option_c': '8',
            'option_d': '9',
            'correct_option': 'B',
            'explanation': 'Pure water has a pH of 7 at 25°C, making it neutral.',
            'difficulty': 'medium'
        },
        {
            'subject_id': subject_id,
            'text': 'Which type of bond is formed when electrons are shared between atoms?',
            'option_a': 'Ionic bond',
            'option_b': 'Metallic bond',
            'option_c': 'Covalent bond',
            'option_d': 'Hydrogen bond',
            'correct_option': 'C',
            'explanation': 'Covalent bonds are formed when electrons are shared between atoms.',
            'difficulty': 'medium'
        }
    ]

def get_biology_questions(subject_id):
    return [
        {
            'subject_id': subject_id,
            'text': 'What is the powerhouse of the cell?',
            'option_a': 'Nucleus',
            'option_b': 'Mitochondria',
            'option_c': 'Ribosome',
            'option_d': 'Endoplasmic reticulum',
            'correct_option': 'B',
            'explanation': 'Mitochondria are called the powerhouse of the cell because they produce ATP energy.',
            'difficulty': 'easy'
        },
        {
            'subject_id': subject_id,
            'text': 'Which process do plants use to make their own food?',
            'option_a': 'Respiration',
            'option_b': 'Photosynthesis',
            'option_c': 'Transpiration',
            'option_d': 'Digestion',
            'correct_option': 'B',
            'explanation': 'Plants use photosynthesis to convert sunlight, carbon dioxide, and water into glucose.',
            'difficulty': 'easy'
        },
        {
            'subject_id': subject_id,
            'text': 'What is the basic unit of heredity?',
            'option_a': 'Chromosome',
            'option_b': 'DNA',
            'option_c': 'Gene',
            'option_d': 'Protein',
            'correct_option': 'C',
            'explanation': 'A gene is the basic unit of heredity that carries genetic information.',
            'difficulty': 'medium'
        },
        {
            'subject_id': subject_id,
            'text': 'How many chambers does a human heart have?',
            'option_a': '2',
            'option_b': '3',
            'option_c': '4',
            'option_d': '5',
            'correct_option': 'C',
            'explanation': 'The human heart has four chambers: two atria and two ventricles.',
            'difficulty': 'medium'
        },
        {
            'subject_id': subject_id,
            'text': 'Which blood type is known as the universal donor?',
            'option_a': 'A',
            'option_b': 'B',
            'option_c': 'AB',
            'option_d': 'O',
            'correct_option': 'D',
            'explanation': 'Type O blood is the universal donor because it can be given to people with any blood type.',
            'difficulty': 'medium'
        }
    ]

def get_math_questions(subject_id):
    return [
        {
            'subject_id': subject_id,
            'text': 'What is the value of π (pi) approximately?',
            'option_a': '3.14',
            'option_b': '2.71',
            'option_c': '1.41',
            'option_d': '1.73',
            'correct_option': 'A',
            'explanation': 'The value of π (pi) is approximately 3.14159...',
            'difficulty': 'easy'
        },
        {
            'subject_id': subject_id,
            'text': 'What is the derivative of x²?',
            'option_a': 'x',
            'option_b': '2x',
            'option_c': 'x²',
            'option_d': '2x²',
            'correct_option': 'B',
            'explanation': 'The derivative of x² is 2x using the power rule.',
            'difficulty': 'medium'
        },
        {
            'subject_id': subject_id,
            'text': 'What is the sum of angles in a triangle?',
            'option_a': '90°',
            'option_b': '180°',
            'option_c': '270°',
            'option_d': '360°',
            'correct_option': 'B',
            'explanation': 'The sum of all angles in any triangle is always 180°.',
            'difficulty': 'easy'
        },
        {
            'subject_id': subject_id,
            'text': 'What is the quadratic formula?',
            'option_a': 'x = -b ± √(b² - 4ac) / 2a',
            'option_b': 'x = b ± √(b² + 4ac) / 2a',
            'option_c': 'x = -b ± √(b² + 4ac) / 2a',
            'option_d': 'x = b ± √(b² - 4ac) / 2a',
            'correct_option': 'A',
            'explanation': 'The quadratic formula is x = (-b ± √(b² - 4ac)) / 2a.',
            'difficulty': 'medium'
        },
        {
            'subject_id': subject_id,
            'text': 'What is the value of sin(90°)?',
            'option_a': '0',
            'option_b': '1',
            'option_c': '√2/2',
            'option_d': '√3/2',
            'correct_option': 'B',
            'explanation': 'The sine of 90° is 1.',
            'difficulty': 'medium'
        }
    ]

def get_general_questions(subject_id, subject_name):
    return [
        {
            'subject_id': subject_id,
            'text': f'This is a sample question for {subject_name}. What is the correct answer?',
            'option_a': 'Option A',
            'option_b': 'Option B',
            'option_c': 'Option C',
            'option_d': 'Option D',
            'correct_option': 'A',
            'explanation': f'This is a sample question for {subject_name} subject.',
            'difficulty': 'medium'
        },
        {
            'subject_id': subject_id,
            'text': f'Another sample question for {subject_name}. Choose the best answer.',
            'option_a': 'First choice',
            'option_b': 'Second choice',
            'option_c': 'Third choice',
            'option_d': 'Fourth choice',
            'correct_option': 'B',
            'explanation': f'This is another sample question for {subject_name} subject.',
            'difficulty': 'medium'
        }
    ]

if __name__ == '__main__':
    add_sample_questions()
