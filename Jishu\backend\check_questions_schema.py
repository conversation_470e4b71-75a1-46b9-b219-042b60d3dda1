from app import create_app
from app import db
import sqlalchemy as sa

app = create_app()
with app.app_context():
    inspector = sa.inspect(db.engine)

    # Check if questions table exists
    tables = inspector.get_table_names()
    print("Available tables:", tables)

    if 'questions' in tables:
        print("\nQuestions table exists:")
        print(inspector.get_columns('questions'))
    else:
        print("\nQuestions table does NOT exist")

    if 'questions_with_options' in tables:
        print("\nQuestions_with_options table exists:")
        print(inspector.get_columns('questions_with_options'))
    else:
        print("\nQuestions_with_options table does NOT exist")
