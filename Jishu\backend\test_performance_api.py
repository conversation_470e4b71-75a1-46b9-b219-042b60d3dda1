#!/usr/bin/env python3
"""Test script for performance summary API"""

import requests
import json
import sys
import os

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Test configuration
BASE_URL = "http://localhost:5000"

def test_performance_api():
    """Test the performance summary API"""
    print("🧪 Testing Performance Summary API")
    print("=" * 50)
    
    # Test without authentication (should work with default user)
    print("1. Testing performance summary endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/api/user/performance-summary")
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Performance summary API working")
            print(f"   Total exams: {data.get('total_exams', 0)}")
            print(f"   Overall accuracy: {data.get('overall_accuracy', 0)}%")
            print(f"   Average score: {data.get('average_score', 0)}")
            print(f"   Subject-wise performance: {len(data.get('subject_wise_performance', {}))}")
            print(f"   Recent attempts: {len(data.get('recent_attempts', []))}")
            return True
        else:
            print(f"❌ API returned status {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error calling API: {str(e)}")
        return False

def test_subject_model():
    """Test Subject model directly"""
    print("\n2. Testing Subject model...")
    try:
        from app import create_app, db
        from app.models.exam import Subject
        
        app = create_app()
        with app.app_context():
            subjects = Subject.query.all()
            print(f"✅ Found {len(subjects)} subjects in database")
            
            for subject in subjects[:3]:  # Show first 3 subjects
                print(f"   - {subject.id}: {subject.subject_name}")
                print(f"     Category ID: {subject.exam_category_id}")
                print(f"     Has category relationship: {hasattr(subject, 'category')}")
                if hasattr(subject, 'category') and subject.category:
                    print(f"     Category name: {subject.category.name}")
                print()
            
            return True
    except Exception as e:
        print(f"❌ Error testing Subject model: {str(e)}")
        return False

def test_exam_attempts():
    """Test ExamAttempt model"""
    print("\n3. Testing ExamAttempt model...")
    try:
        from app import create_app, db
        from app.models.user_progress import ExamAttempt
        from app.models.payment import Purchase
        
        app = create_app()
        with app.app_context():
            attempts = ExamAttempt.query.all()
            print(f"✅ Found {len(attempts)} exam attempts in database")
            
            for attempt in attempts[:3]:  # Show first 3 attempts
                print(f"   - Attempt {attempt.id}: User {attempt.user_id}")
                print(f"     Purchase ID: {attempt.purchase_id}")
                print(f"     Score: {attempt.score}")
                print(f"     Completed: {attempt.completed_at}")
                
                # Test purchase relationship
                purchase = Purchase.query.get(attempt.purchase_id)
                if purchase:
                    print(f"     Purchase subject ID: {purchase.subject_id}")
                print()
            
            return True
    except Exception as e:
        print(f"❌ Error testing ExamAttempt model: {str(e)}")
        return False

if __name__ == "__main__":
    try:
        success = True
        success &= test_performance_api()
        success &= test_subject_model()
        success &= test_exam_attempts()
        
        print("\n" + "=" * 50)
        if success:
            print("🎉 All tests passed!")
        else:
            print("❌ Some tests failed")
            sys.exit(1)
    except Exception as e:
        print(f"❌ Test suite failed with error: {str(e)}")
        sys.exit(1)
