// Test API connection from React Native app
const { API_URL } = require('./src/config/index');

console.log('Testing API connection...');
console.log('API URL:', API_URL);

// Test health endpoint
fetch(`${API_URL}/health`)
  .then(response => response.json())
  .then(data => {
    console.log('Health check response:', data);
    
    // Test purchase endpoint
    return fetch(`${API_URL}/api/user/exams/purchase`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ subject_id: 1 })
    });
  })
  .then(response => response.json())
  .then(data => {
    console.log('Purchase API response:', data);
    
    // Test MCQ generation endpoint
    return fetch(`${API_URL}/api/ai/generate-mcq-for-exam`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ subject_id: 1, num_questions: 5 })
    });
  })
  .then(response => response.json())
  .then(data => {
    console.log('MCQ generation API response:', data);
    console.log('All API tests passed!');
  })
  .catch(error => {
    console.error('API test failed:', error);
  });
