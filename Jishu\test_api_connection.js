// Test API connection from React Native app
const { API_URL } = require('./src/config/index');

console.log('Testing API connection...');
console.log('API URL:', API_URL);

// Test the exact sequence that happens when clicking "Start Exam"
async function testStartExamSequence() {
  try {
    console.log('\n=== Testing Start Exam Sequence ===');

    // Step 1: Test MCQ generation (this happens first in TestInstructions.jsx)
    console.log('Step 1: Testing MCQ generation...');
    const mcqResponse = await fetch(`${API_URL}/api/ai/generate-mcq-for-exam`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        subject_id: 1,
        num_questions: 50,
        use_pdf_content: true
      })
    });

    const mcqData = await mcqResponse.json();
    console.log('MCQ generation response:', mcqData);

    if (mcqData.status === 'error') {
      console.error('MCQ generation failed:', mcqData.message);
      return;
    }

    // Step 2: Test purchase endpoint
    console.log('\nStep 2: Testing exam purchase...');
    const purchaseResponse = await fetch(`${API_URL}/api/user/exams/purchase`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ subject_id: 1 })
    });

    const purchaseData = await purchaseResponse.json();
    console.log('Purchase response:', purchaseData);

    // Step 3: Test questions endpoint (this is what ExamQuestionsScreen calls)
    console.log('\nStep 3: Testing questions retrieval...');
    const questionsResponse = await fetch(`${API_URL}/api/questions/subject/1?count=50`);
    const questionsData = await questionsResponse.json();
    console.log('Questions response:', questionsData.length, 'questions retrieved');

    console.log('\n✅ All Start Exam sequence tests passed!');

  } catch (error) {
    console.error('❌ Start Exam sequence test failed:', error);
  }
}

// Run the test
testStartExamSequence();
